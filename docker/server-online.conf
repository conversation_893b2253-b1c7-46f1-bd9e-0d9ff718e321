server {
    listen       80;
    server_name  localhost;
    client_max_body_size 100M;
    location / {

      access_by_lua_block {
        require('cas').force_authentication()
      }
       
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
         try_files $uri $uri/ /index.html;
        # 清除html的缓存
        add_header Cache-Control no-store;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }

    # 暂时 - 使用 mock 环境 
    location ^~/api/ {
      access_by_lua_block {
        local params_args = ngx.req.get_uri_args();
        params_args['_appId'] = "mcp-store";
        ngx.req.set_uri_args(params_args);
        require('cas').append_user_to_query('account',403,"'login error'");
        
      }
      proxy_pass http://mcpstore.api.corpautohome.com/;
    }



    # 4、引入sso公共接口
    include servers/cas-location.conf;

    location /servers/detail/public/demo.gif {
        alias /usr/local/openresty-1.13.6.2/nginx/html/demo.gif;
    }
}
