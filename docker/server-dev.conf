server {
    listen       80;
    server_name  localhost;
    location / {
       
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
         try_files $uri $uri/ /index.html;
        # 清除html的缓存
        add_header Cache-Control no-store;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }


    location ^~/api/ {
      proxy_pass http://mcpstore.api.mulan.corpautohome.com/;
    }

    # 需求二、地址跳转代理
    # rewrite ~*/app/(.*)/(.*) /$1.html last;

    location ~* /sso/username$ {
        access_by_lua_block {
            require('cas').user_name();
        }
    }

    location ~* /sso/username/redirect$ {
        access_by_lua_block {
            require('cas').user_name_redirect();
        }
    }

    location ~* /sso/logout$ {
        access_by_lua_block {
           
            require('cas').logout_redirect();
            
        }
    }
}
