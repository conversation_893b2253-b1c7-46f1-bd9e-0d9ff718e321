{"terminal.integrated.fontSize": 12, "editor.fontSize": 12, "editor.fontFamily": "'JetBrainsMono Nerd <PERSON>'", "files.autoSave": "after<PERSON>elay", "editor.fontWeight": "500", "editor.fontLigatures": false, "liveServer.settings.port": 5501, "eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "vue"], "editor.codeActionsOnSave": {"source.formatDocument": "explicit", "source.fixAll.eslint": "explicit"}, "eslint.lintTask.enable": true, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.formatDocument": "explicit", "source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.formatDocument": "explicit", "source.fixAll.eslint": "explicit"}}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": false, "problems.autoReveal": true, "eslint.workingDirectories": [{"mode": "auto"}], "prettier.requireConfig": true, "prettier.useEditorConfig": false}