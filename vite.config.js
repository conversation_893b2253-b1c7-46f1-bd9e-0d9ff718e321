import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';

// 修改配置导出方式
export default defineConfig(({ command, mode }) => {
    // 加载环境变量
    const env = loadEnv(mode, process.cwd());

    // 获取 CDN 配置
    const { CDN_PROJECT_NAME } = env;
    const projectName = CDN_PROJECT_NAME?.split('/')[0] || 'dealer_microfe_aidev';
    const cdnPrefix = CDN_PROJECT_NAME?.split('/')[1] || 'mcp-store';

    // 区分开发环境和构建环境
    const isDev = command === 'serve'; // 本地开发环境

    // 根据环境确定 base 路径
    // 开发环境使用本地路径，构建环境使用 CDN 路径
    const base = isDev ? '/' : `https://z.autoimg.cn/${projectName}/${cdnPrefix}/static/`;

    // 插件数组
    const plugins = [vue()];

    // 是否保留 console 和 debugger
    // 开发环境保留，构建环境移除
    const preserveDebugInfo = isDev;

    return {
        base,
        plugins,
        css: {
            preprocessorOptions: {
                scss: {
                    // 使用@use代替@import，避免Sass弃用警告
                    additionalData: `@use "@/assets/styles/iphoneColor.scss" as *;`,
                    // 使用现代Sass编译器API，支持@use模块系统
                    api: 'modern',
                },
            },
        },
        build: {
            // 配置打包输出目录，统一输出到 dist 目录下
            outDir: 'dist',
            // 启用 CSS 代码分割
            cssCodeSplit: true,
            // 启用源码映射以便于调试（仅在测试环境启用）
            sourcemap: env.VITE_ENABLE_SOURCEMAP === 'true',
            // 启用压缩
            minify: 'terser',
            // terser 配置
            terserOptions: {
                compress: {
                    drop_console: !preserveDebugInfo,
                    drop_debugger: !preserveDebugInfo,
                },
            },
            // 启用 gzip 压缩
            brotliSize: false,
            // 分块策略
            chunkSizeWarningLimit: 1000,
            rollupOptions: {
                output: {
                    // 使用代码分割优化首屏加载
                    manualChunks: {
                        vendor: ['vue', 'vue-router', 'pinia'],
                        // 其他第三方库可以根据需要添加
                    },
                    // 设置输出文件名，添加内容哈希以便缓存
                    entryFileNames: `assets/[name].[hash].js`,
                    chunkFileNames: `assets/[name].[hash].js`,
                    assetFileNames: `assets/[name].[hash].[ext]`,
                },
            },
        },
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url)),
            },
        },
        // 优化依赖预构建
        optimizeDeps: {
            include: ['vue', 'vue-router', 'pinia'],
            // 可以根据项目需要添加更多依赖
        },
        server: {
            host: true,
            port: 9000,
            proxy: {
                '^/api': {
                    target: 'http://mcpstore.api.mulan.corpautohome.com',
                    changeOrigin: true,
                    secure: false,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                    configure: (proxy) => {
                        proxy.on('proxyReq', (proxyReq, req) => {
                            const accountParam = '_appId=mcp-store&account=zhaixiaowei';
                            const delimiter = req.url.includes('?') ? '&' : '?';
                            proxyReq.path += delimiter + accountParam;
                            console.log(`Proxying request to: ${req.url}`); // 输出代理请求的路径
                        });
                    },
                },
                '^/mock': {
                    target: 'http://doc.phonebus.corpautohome.com',
                    changeOrigin: true,
                    secure: false,
                    configure: (proxy) => {
                        proxy.on('proxyReq', (proxyReq, req) => {
                            const accountParam = '_appId=mcp-store&account=zhaixiaowei';
                            const delimiter = req.url.includes('?') ? '&' : '?';
                            proxyReq.path += delimiter + accountParam;
                            console.log(`Proxying request to: ${req.url}`); // 输出代理请求的路径
                        });
                    },
                },
            },
        },
        // 添加 esbuild 配置
        esbuild: {
            drop: preserveDebugInfo ? [] : ['console', 'debugger'],
        },
    };
});
