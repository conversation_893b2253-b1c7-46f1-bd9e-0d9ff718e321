<template>
    <ServerLayout>
        <!-- 顶部标题区域 -->
        <template #header>
            <div class="header-container">
                <h2 class="content-title">API Keys</h2>
                <Button
                    label="创建新的API Keys"
                    icon="pi pi-plus"
                    class="submit-button"
                    @click="showCreateKeyDialog"
                />
            </div>
        </template>

        <!-- 主要内容区域 -->
        <div class="content-container">
            <div class="api-key-info">
                <p>不要与他人共享您的 API Keys，或在浏览器或其他客户端端代码中暴露它。</p>
            </div>

            <!-- API密钥列表 -->
            <DataTable
                :value="apiKeys"
                striped-rows
                class="p-datatable-sm"
                table-style="min-width: 800px"
            >
                <Column field="name" header="姓名"></Column>
                <Column field="key" header="API Keys">
                    <template #body="{ data }">
                        <div class="key-value">{{ maskApiKey(data.key) }}</div>
                    </template>
                </Column>
                <Column field="createdAt" header="创建时间"></Column>
                <Column field="lastUsedAt" header="最后使用时间"></Column>
                <Column header="操作" header-style="width: 96px">
                    <template #body="{ data }">
                        <Button
                            icon="pi pi-pencil"
                            class="p-button-text p-button-rounded"
                            @click="editApiKey(data)"
                        />
                    </template>
                </Column>
            </DataTable>
        </div>

        <!-- 创建密钥对话框 -->
        <Dialog
            v-model:visible="createKeyDialogVisible"
            modal
            header="创建"
            :style="{ width: '450px' }"
            :closable="true"
        >
            <div class="p-field">
                <label for="keyName">API Keys名称</label>
                <InputText
                    id="keyName"
                    v-model="newKeyName"
                    class="p-inputtext-sm"
                    style="width: 100%"
                />
            </div>
            <template #footer>
                <Button
                    label="取消"
                    class="p-button-text"
                    @click="createKeyDialogVisible = false"
                />
                <Button label="创建" @click="createNewKey" />
            </template>
        </Dialog>
    </ServerLayout>
</template>

<script setup>
import { ref } from 'vue';
import ServerLayout from '../my-servers/components/layout.vue';

const createKeyDialogVisible = ref(false);
const newKeyName = ref('');

// Mock API Keys数据
const apiKeys = ref([
    {
        id: 1,
        name: '光标',
        key: 'eyJhbG..L3GoLg',
        createdAt: '2025年4月27日 20:16:32',
        lastUsedAt: '2025年4月27日 20:16:32',
    },
]);

// 显示创建密钥对话框
const showCreateKeyDialog = () => {
    createKeyDialogVisible.value = true;
    newKeyName.value = '';
};

// 创建新的API密钥
const createNewKey = () => {
    if (!newKeyName.value.trim()) {
        return;
    }

    const now = new Date();
    const formattedDate =
        `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日 ` +
        `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;

    // 创建随机密钥
    const randomKey =
        'eyJhbG' +
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);

    apiKeys.value.push({
        id: apiKeys.value.length + 1,
        name: newKeyName.value,
        key: randomKey,
        createdAt: formattedDate,
        lastUsedAt: formattedDate,
    });

    createKeyDialogVisible.value = false;
};

// 编辑API密钥
const editApiKey = (data) => {
    console.log('编辑密钥:', data);
};

// 隐藏部分API密钥
const maskApiKey = (key) => {
    if (!key) {
        return '';
    }
    if (key.length <= 10) {
        return key;
    }
    return key.substring(0, 10) + '...';
};
</script>

<style scoped lang="scss">
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.content-title {
    font-size: $heading-4;
    font-weight: $font-weight-semibold;
    color: $label-primary;
    margin: 0;
}

.submit-button {
    background-color: $system-blue;
    border-color: $system-blue;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
}

.content-container {
    padding: 0;
}

.api-key-info {
    margin-bottom: 24px;

    p {
        margin: 8px 0;
        font-size: $font-size-sm;
        color: $label-secondary;
        line-height: 1.5;
    }
}

:deep(.p-datatable) {
    .p-datatable-header {
        padding: 16px;
    }

    .p-datatable-thead > tr > th {
        font-weight: $font-weight-medium;
        padding: 16px;
        font-size: $font-size-base;
    }

    .p-datatable-tbody > tr > td {
        padding: 14px 16px;
        font-size: $font-size-base;
    }
}
</style>
