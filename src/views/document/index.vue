<template>
    <div class="doc-home">
        <div v-if="loading" class="position-absolute top-0 left-0 w-full h-full">
            <Loading />
        </div>
        <iframe
            src="https://z.autoimg.cn/dealer_microfe_aidev/mcp/docs/docsify/docs/0603/guide/index.html"
            frameborder="0"
            width="100%"
            height="100%"
            @load="loading = false"
        ></iframe>
    </div>
</template>

<script setup>
import Loading from '@/components/common/Loading.vue';
import { ref } from 'vue';

const loading = ref(true);
</script>

<style scoped>
.doc-home {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
