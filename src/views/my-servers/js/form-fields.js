import { validators } from '@/utils/validators';

/**
 * 服务器表单字段配置
 */
export const formFields = [
    {
        name: 'name',
        label: 'MCP Server 名称',
        type: 'text',
        required: true,
        validators: [validators.required, validators.maxLength(100)],
        disabled: false,
        hint: '填写您的MCP Server名称，简洁明了（最多100个字符）',
        showExample: true,
        exampleTitle: '名称示例',
        exampleDescription: '一个好的名称应该简洁明了地表达服务的主要功能',
        exampleData: 'MCP文档解析服务',
    },
    {
        name: 'gitUrl',
        label: 'Git 仓库地址',
        type: 'text',
        required: false,
        validators: [validators.maxLength(200)],
        disabled: false,
        hint: '填写Git代码仓库的完整URL，例如: https://git.corpautohome.com/dealer-arch-doc/learn-share/tech-explore（最多200个字符）',
    },
    {
        name: 'description',
        label: '简要描述',
        type: 'textarea',
        required: true,
        validators: [validators.required, validators.maxLength(500)],
        disabled: false,
        hint: '简要描述您的MCP Server功能和特点（最多500个字符）',
        showExample: true,
        exampleTitle: '描述示例',
        exampleDescription: '一个好的描述应该简洁地概括MCP Server的主要功能和价值',
        exampleData:
            '这是一个基于MCP协议的文档解析服务，能够高效处理多种格式的文档，支持文本提取、结构化数据识别和内容摘要功能。',
    },
    {
        name: 'tags',
        label: '标签',
        type: 'textarea',
        required: false,
        placeholder: '用逗号分隔多个标签',
        disabled: false,
        hint: '用逗号分隔多个标签，如：文档处理,数据提取,AI服务。支持从Git仓库地址自动同步标签。',
        showExample: true,
        exampleTitle: '标签示例',
        exampleDescription: '添加3-5个相关标签，用逗号分隔',
        exampleData: '文档处理,PDF解析,OCR识别,数据提取,AI服务',
    },
    {
        name: 'programLanguage',
        label: '编程语言',
        type: 'radio',
        required: true,
        validators: [validators.required],
        disabled: false,
        hint: '选择MCP Server主要使用的编程语言',
        options: [
            { label: 'Python', value: 'Python' },
            { label: 'TypeScript', value: 'TypeScript' },
            { label: 'Java', value: 'Java' },
            { label: 'Kotlin', value: 'Kotlin' },
            { label: 'C#', value: 'C#' },
            { label: 'Swift', value: 'Swift' },
        ],
    },
    {
        name: 'serverCategories',
        label: '所属类别',
        type: 'multiselect',
        required: true,
        validators: [
            {
                check: (value) => value && value.length > 0,
                message: '请选择至少一个类别',
            },
        ],
        options: [],
        disabled: false,
        hint: '选择服务所属的一个或多个类别',
    },
    {
        name: 'serverConfig',
        label: '服务配置',
        type: 'textarea',
        required: true,
        validators: [validators.required, validators.maxLength(500)],
        rows: 10,
        isCode: true,
        disabled: false,
        hint: '填写服务的配置JSON，包含服务的详细参数设置（最多500个字符）。可切换查看不同平台的配置示例。',
        showExample: true,
        exampleTitle: '服务配置示例',
        exampleDescription: '请根据您使用的平台选择对应的配置示例',
        exampleData: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`,
        platforms: [
            {
                type: 'stdio',
                name: 'stdio 类型',
                platforms: [
                    {
                        name: 'Claude Desktop',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`,
                    },
                    {
                        name: 'Cursor',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`,
                    },
                    {
                        name: 'Docker',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "mcp/sequentialthinking"
      ]
    }
  }
}`,
                    },
                    {
                        name: '本地开发',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/your/server",
        "run",
        "server.py"
      ]
    }
  }
}`,
                    },
                ],
            },
            {
                type: 'sse',
                name: 'sse 类型',
                platforms: [
                    {
                        name: 'Claude Desktop',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "type": "sse",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`,
                    },
                    {
                        name: 'Cursor',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "type": "sse",
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    }
  }
}`,
                    },
                    {
                        name: '远程服务',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "type": "sse",
      "url": "https://your-mcp-server-url.com/api/mcp/sse?profile=<scope-name>/<profile-name>"
    }
  }
}`,
                    },
                ],
            },
            {
                type: 'custom',
                name: '自定义环境变量',
                platforms: [
                    {
                        name: 'API Key配置',
                        content: `{
  "mcpServers": {
    "sequential-thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ],
      "env": {
        "API_KEY": "your_api_key_here",
        "DEBUG": "true"
      }
    }
  }
}`,
                    },
                    {
                        name: '本地路径配置',
                        content: `{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Documents",
        "/Users/<USER>/Downloads"
      ]
    }
  }
}`,
                    },
                ],
            },
        ],
    },
    {
        name: 'introduction',
        label: '简介',
        type: 'markdown',
        required: true,
        validators: [validators.required],
        disabled: false,
        hint: '详细介绍服务的功能、特点、使用方法等。可自动同步Git仓库的README.md内容，保存为markdown格式。',
        showExample: true,
        exampleTitle: '简介示例',
        exampleDescription:
            '这是一个标准的服务简介模板，包含服务概述、功能特点、使用方法和示例代码',
        exampleData: `# MCP文档解析服务

## 服务概述

MCP文档解析服务是一个专为开发者设计的高效文档处理工具，基于先进的NLP技术，能够快速解析多种格式的文档，提取关键信息，并生成结构化数据。

## 核心功能

- **多格式支持**：支持PDF、Word、Excel、HTML等多种格式的文档解析
- **智能信息提取**：自动识别文档中的关键信息和数据结构
- **内容分类**：对文档内容进行智能分类和标签化
- **跨语言支持**：支持中英文等多种语言的文档处理

## 快速入门

### 安装

\`\`\`bash
npm install mcp-doc-parser
\`\`\`

### 基本用法

\`\`\`javascript
import { MCPDocParser } from 'mcp-doc-parser';

// 初始化解析器
const parser = new MCPDocParser();

// 解析文档
const result = await parser.parse('document.pdf');
console.log(result);
\`\`\`

## 使用场景

- 文档自动化处理
- 信息快速提取与归档
- 数据分析与挖掘
- 内容智能推荐`,
    },
    {
        name: 'logoUrl',
        label: 'Logo 地址',
        type: 'text',
        required: false,
        validators: [validators.maxLength(200)],
        disabled: false,
        hint: '填写服务Logo图片的URL地址（最多200个字符）',
        placeholder: '请输入Logo图片的URL地址',
    },
    {
        name: 'gitStarCount',
        label: 'Git Star 数量',
        type: 'text',
        required: true,
        validators: [validators.required],
        defaultValue: 0,
        disabled: true,
        hint: 'Git仓库的Star数量，系统自动获取，不支持修改',
    },
];
