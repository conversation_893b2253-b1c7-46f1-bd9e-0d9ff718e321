import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import PrimeVue from 'primevue/config';
import 'primevue/resources/themes/aura-light-blue/theme.css';
import './style.scss';
import registerPrimeVue from './registerPrimeVue';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';

// 导入 v-md-editor
import VMdEditor from '@kangc/v-md-editor';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';

// 导入代码高亮
import hljs from 'highlight.js';

VMdEditor.use(githubTheme, {
    Hljs: hljs,
});

const app = createApp(App);

app.use(VMdEditor);

app.use(ToastService);
app.use(ConfirmationService);

app.use(router);
app.use(createPinia());

app.use(PrimeVue);
registerPrimeVue(app);

app.mount('#app');
