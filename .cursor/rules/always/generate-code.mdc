---
description: 
globs: 
alwaysApply: true
---
# 代码生成标准规范

## 概述

所有生成或修改的代码必须严格遵循项目的金标准规范。这些规范基于项目中表现最优秀的文件实践，确保代码质量的一致性和可维护性。

## 📋 核心规范文件

本项目的代码规范基于以下金标准文件：

### 🎯 Vue组件标准
- **金标准文件**：`src/components/common/FadeInSection.vue`
- **规范文件**：`.cursor/rules/always/vue-component-standard.mdc`
- **适用范围**：所有Vue单文件组件

### 🎯 状态管理标准  
- **金标准文件**：`src/stores/iframe.js` + `src/composables/useBotSelector.js`
- **规范文件**：`.cursor/rules/always/pinia-composable-standard.mdc`
- **适用范围**：Pinia stores 和 composable 函数

## 🚀 代码生成要求

### 1. 强制性规范
生成任何代码时必须：
- ✅ 遵循对应的金标准规范
- ✅ 使用函数式编程而非面向对象编程（用户规则）
- ✅ 包含完整的注释和文档
- ✅ 具备错误处理机制
- ✅ 保持代码结构清晰

### 2. Vue组件生成规范
```vue
<!-- 必须遵循的结构模板 -->
<template>
    <div class="component-name" :class="conditionalClasses">
        <slot></slot>
    </div>
</template>

<script setup>
// 导入依赖
import { ref, computed, onMounted } from 'vue';

// Props定义（必须包含类型、默认值、注释）
const props = defineProps({
    // 属性说明
    propName: {
        type: String,
        default: 'defaultValue'
    }
});

// 响应式状态
const state = ref(initialValue);

// 计算属性
const computed = computed(() => {
    return calculation;
});

// 方法（必须包含JSDoc）
/**
 * 方法说明
 * @param {type} param - 参数说明
 */
const method = (param) => {
    // 实现
};

// 暴露方法
defineExpose({ method });

// 生命周期
onMounted(() => {
    // 初始化逻辑
});
</script>

<style lang="scss" scoped>
.component-name {
    /* 样式规范 */
}
</style>
```

### 3. Store生成规范
```javascript
// 必须遵循的Store模板
import { defineStore } from 'pinia';

export const useModuleStore = defineStore('moduleName', {
    state: () => ({
        // 状态定义（必须有注释）
    }),
    getters: {
        // 计算属性
    },
    actions: {
        /**
         * 方法说明（必须有JSDoc）
         * @param {type} param - 参数说明
         * @returns {Promise} 返回值说明
         */
        async methodName(param) {
            try {
                // 实现逻辑
            } catch (error) {
                console.error('错误信息:', error);
                throw error;
            }
        }
    }
});
```

## 分析要求

请按照以下三个维度详细解释您生成的代码：

### 1. 功能分析
- **作用说明**：这部分代码的核心功能是什么？
- **业务价值**：解决了什么实际问题？
- **使用场景**：在什么情况下会用到这段代码？

### 2. 实现机制
- **执行流程**：代码是如何一步步实现功能的？
- **关键逻辑**：核心算法或处理逻辑是什么？
- **数据流转**：数据在代码中是如何流动和变化的？

### 3. 设计决策
- **方案对比**：考虑过哪些其他的实现方案？
- **选择理由**：为什么最终选择当前这个方案？
- **权衡考虑**：在性能、可维护性、复杂度等方面做了哪些权衡？

## 输出格式

在提供代码后，请按照以下结构进行说明：

```markdown
## 代码解释

### 功能分析
[详细说明代码的作用和价值]

### 实现机制
[逐步解释代码的执行流程]

### 设计决策
[解释选择此方案的原因和考虑因素]
```

## 注意事项

- 解释应该清晰易懂，避免过于技术化的术语
- 重点突出核心思路和关键决策点
- 适当提及潜在的优化空间或注意事项